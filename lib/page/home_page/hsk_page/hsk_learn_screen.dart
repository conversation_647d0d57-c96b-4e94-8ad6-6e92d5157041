import 'dart:math';
import 'dart:async';
import 'dart:convert';
import 'dart:ui'; // Added for ImageFilter

import 'package:flutter/foundation.dart';
import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/models/hsk_settings.dart';
import 'package:dasso_reader/models/java_learn_adapter.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/config/typography.dart';
import 'package:dasso_reader/config/app_typography.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/widgets/decorations/mountain_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dasso_reader/widgets/responsive_button_text.dart';

// Helper class for background saving
/// A helper class responsible for saving HSK learning progress
/// to [SharedPreferences] on the main thread with async optimization.
///
/// Note: SharedPreferences cannot be used in background isolates due to
/// platform channel limitations. This class uses main thread with
/// microtask scheduling to avoid UI blocking.
class BackgroundSaveHelper {
  /// Saves the provided list of [MyPair] (key-value pairs representing progress)
  /// to SharedPreferences. This operation is performed asynchronously on the
  /// main thread using microtasks to minimize UI impact.
  static Future<void> saveProgress(List<MyPair> pairs) async {
    // Schedule on microtask to avoid blocking current frame
    return Future.microtask(() => _saveAsync(pairs));
  }

  /// Internal method that performs the actual saving asynchronously.
  /// It retrieves existing progress, updates it with the new pairs, and writes
  /// it back to [SharedPreferences] in a non-blocking manner.
  static Future<void> _saveAsync(List<MyPair> pairs) async {
    try {
      // Get shared preferences instance
      final prefs = await SharedPreferences.getInstance();

      // Get current progress
      final progressJson = prefs.getString('hsk_learn_progress') ?? '{}';
      final Map<String, dynamic> progress = jsonDecode(progressJson);

      // Update progress with new pairs
      for (var pair in pairs) {
        final key = pair.key.toString();
        final value = pair.value;
        progress[key] = value;
      }

      // Save back to shared preferences
      await prefs.setString('hsk_learn_progress', jsonEncode(progress));
    } catch (e) {
      // Log error but don't crash the app
      debugPrint('Error saving HSK progress: $e');
    }
  }
}

/// A screen that provides an interactive learning experience for HSK characters.
///
/// It guides the user through various stages, presenting characters, pinyin,
/// and English translations as prompts and multiple-choice answers.
/// The screen utilizes [JavaLearnAdapter] to manage the learning logic and progression.
class HskLearnScreen extends ConsumerStatefulWidget {
  /// The set of HSK characters to be learned in this session.
  final HskCharacterSet characterSet;

  /// Creates an [HskLearnScreen].
  ///
  /// Requires a [characterSet] to define the learning content.
  const HskLearnScreen({
    super.key,
    required this.characterSet,
  });

  @override
  ConsumerState<HskLearnScreen> createState() => _HskLearnScreenState();
}

/// The state for the [HskLearnScreen].
///
/// Manages the UI, animations, user interactions, audio playback,
/// and communication with the [JavaLearnAdapter] and Riverpod providers.
class _HskLearnScreenState extends ConsumerState<HskLearnScreen>
    with TickerProviderStateMixin {
  late JavaLearnAdapter _learnAdapter;
  late HskCharacter _currentCharacter;
  late List<HskCharacter> _characters;
  late List<dynamic> _choices =
      []; // Can be either characters or pinyin/English
  late bool _showingFeedback = false;
  late int _selectedChoiceIndex = -1;
  final AudioPlayer _audioPlayer = AudioPlayer();
  int _missesCount = 0;

  // Track which choices have been selected incorrectly
  Set<int> _incorrectChoices = {};

  // =====================================================
  // STATIC OPTIMIZED COMPONENTS - Performance Enhancement
  // =====================================================

  /// Theme-aware background gradient with WCAG AAA compliance
  /// Replaces hardcoded colors with Material Design 3 theme colors
  BoxDecoration _getBackgroundGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          colorScheme.primaryContainer,
          colorScheme.secondaryContainer,
        ],
      ),
    );
  }

  // Note: Static colors replaced with theme-aware ColorSystem.getHSKColors()
  // This ensures consistent theming across light/dark modes

  /// Get theme-aware button decoration colors
  Color get _buttonGlassColor =>
      Theme.of(context).colorScheme.surface.withValues(alpha: 0.15);
  Color get _buttonBorderColor =>
      Theme.of(context).colorScheme.outline.withValues(alpha: 0.25);
  Color get _buttonBorderColorStrong =>
      Theme.of(context).colorScheme.outline.withValues(alpha: 0.3);
  Color get _buttonSplashColor =>
      Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2);
  Color get _buttonHighlightColor =>
      Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1);

  // For animations
  late AnimationController _transitionController;
  late AnimationController _correctAnswerController;
  late AnimationController _wrongAnswerController;
  int _previousDisplaySize = 0;
  int _lastDisplayPos = 0; // Track the last display position like in Java
  String _currentTransition = "";
  bool _isAnimating = false;

  // For button animations
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  int _animatingButtonIndex = -1; // Used in animations

  // For answer animations
  bool _showingCorrectAnimation = false;
  bool _showingWrongAnimation = false;

  // --- UI Constants - Using DesignSystem ---
  static const double _kMinButtonWidth = 90.0;
  static const double _kMaxButtonWidth = 150.0;
  static const double _kMinButtonHeight = 60.0;
  static const double _kMaxButtonHeight = 100.0;

  // Further reduced font sizes to ensure pinyin fits on all devices
  static const double _kBaseFontSizeMultiplier =
      0.75; // Further reduced from 0.9
  static const double _kCharFontSize =
      26.0 * _kBaseFontSizeMultiplier; // Further reduced
  // Using DesignSystem for consistent border radius
  static double get _kButtonBorderRadius =>
      DesignSystem.radiusM; // 16.0 (preserves exact radius)
  // Using DesignSystem for consistent padding
  static EdgeInsets get _kButtonPadding => EdgeInsets.symmetric(
      horizontal: DesignSystem.spaceXS,
      vertical: DesignSystem.spaceXS - 1); // 4.0, 3.0 (preserves exact spacing)
  static EdgeInsets get _kButtonContentPadding => EdgeInsets.symmetric(
      horizontal: DesignSystem.spaceXS + 2,
      vertical: DesignSystem.spaceXS); // 6.0, 4.0 (preserves exact spacing)

  // --- Animation Durations - Using DesignSystem ---
  static Duration get _kDefaultTransitionDuration =>
      DesignSystem.durationMedium; // 300ms (preserves exact timing)
  static Duration get _kOneThreeTransitionDuration =>
      DesignSystem.durationMedium + const Duration(milliseconds: 50); // 350ms
  static Duration get _kThreeFiveTransitionDuration =>
      DesignSystem.durationSlow; // 400ms (preserves exact timing)
  static Duration get _kFiveEightTransitionDuration =>
      DesignSystem.durationSlow + const Duration(milliseconds: 50); // 450ms
  static Duration get _kFeedbackAnimationDelay =>
      DesignSystem.durationMedium; // 300ms (preserves exact timing)
  static const Duration _kEndSessionDelay = Duration(milliseconds: 2000);
  static Duration get _kHapticFeedbackDelay =>
      DesignSystem.durationMedium - const Duration(milliseconds: 100); // 200ms
  static Duration get _kButtonPulseDuration =>
      DesignSystem.durationFast; // 150ms (preserves exact timing)

  // --- Learning Logic Constants ---
  static const int _kTotalLearningStages = 9;

  // --- Prompt Area Constants - Using DesignSystem ---
  static const double _kPromptMinHeight = 100.0;
  static const double _kPromptMaxHeight = 180.0;
  static EdgeInsets get _kPromptPadding => EdgeInsets.symmetric(
      vertical: DesignSystem.spaceM + 4,
      horizontal: DesignSystem.spaceM); // 20.0, 16.0 (preserves exact spacing)
  static double get _kPromptBorderRadius =>
      DesignSystem.radiusM; // 16.0 (preserves exact radius)

  // --- Final Stage Prompt Constants - Using DesignSystem ---
  static const double _kFinalPromptCharSize = 60.0; // For FittedBox
  static const double _kFinalPromptCongratsFontSize =
      22.0; // Target size for FittedBox
  static const double _kFinalPromptSubTextFontSize = 14.0;
  static const double _kFinalPromptPinyinFontSize =
      26.0; // Target size for FittedBox
  static const double _kFinalPromptEnglishFontSize =
      20.0; // Target size for FittedBox

  static double get _kFinalPromptSpacing1 =>
      DesignSystem.spaceS; // 8.0 (preserves exact spacing)
  static double get _kFinalPromptSpacing2 =>
      DesignSystem.spaceXS; // 4.0 (preserves exact spacing)
  static double get _kFinalPromptTextSpacing =>
      DesignSystem.spaceXS; // 4.0 (preserves exact spacing)

  @override
  void initState() {
    super.initState();
    _characters = widget.characterSet.characters.toList();

    // Initialize animation controllers
    _transitionController = AnimationController(
      vsync: this,
      duration: _kDefaultTransitionDuration,
    );

    _correctAnswerController = AnimationController(
      vsync: this,
      duration: _kDefaultTransitionDuration,
    );

    _wrongAnswerController = AnimationController(
      vsync: this,
      duration: _kDefaultTransitionDuration,
    );

    // Setup animations
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(
      CurvedAnimation(
        parent: _transitionController,
        curve: Curves.easeOutBack,
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _transitionController,
        curve: Curves.easeIn,
      ),
    );

    _transitionController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });

    _correctAnswerController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _showingCorrectAnimation = false;
        });
      }
    });

    _wrongAnswerController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _showingWrongAnimation = false;
        });
      }
    });

    // Initialize the Java-style learning adapter
    _learnAdapter = JavaLearnAdapter(_characters);

    // Start the first round
    _startNewRound();

    // Start the learn session
    Future.microtask(() {
      ref.read(hskSessionProgressProvider.notifier).startLearnSession(
            _characters.length,
          );
    });

    // Schedule playing the audio after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _playAudio();
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _transitionController.dispose();
    _correctAnswerController.dispose();
    _wrongAnswerController.dispose();

    // End the learn session and update the total view time
    // Use try-catch to safely access ref before disposal
    try {
      if (mounted) {
        ref.read(hskSessionProgressProvider.notifier).endSession();
      }
    } catch (e) {
      // Widget already disposed, ignore the error
      if (kDebugMode) {
        debugPrint(
            'HSK Learn Screen: Session already ended or widget disposed');
      }
    }

    super.dispose();
  }

  // Audio error state
  bool _audioError = false;
  String _audioErrorMsg = "";

  /// Plays the audio for the [_currentCharacter].
  ///
  /// It respects the 'autoPlaySound' setting. If playback fails,
  /// it attempts a fallback path and updates [_audioError] state.
  void _playAudio() async {
    final settings = await ref.read(hskLearnSettingsNotifierProvider.future);
    if (settings.autoPlaySound) {
      setState(() {
        _audioError = false; // Reset error state
      });

      try {
        // Stop any current playback
        await _audioPlayer.stop();

        // Get the audio path
        final audioPath = _currentCharacter.audioAssetPath;

        // Set volume to max
        await _audioPlayer.setVolume(1.0);

        // Use haptic feedback to indicate we're playing audio
        HapticFeedback.mediumImpact();

        // Play audio with asset source
        await _audioPlayer.play(AssetSource(audioPath));
      } catch (e) {
        // Try alternate path format as fallback (similar to Java's approach)
        try {
          final hskNum =
              _currentCharacter.hskLevel.replaceAll(RegExp(r'[^0-9]'), '');
          final fallbackPath =
              'audio/hsk$hskNum/${_currentCharacter.characterId}.mp3';

          await _audioPlayer.play(AssetSource(fallbackPath));
        } catch (fallbackError) {
          // Store error state for subtle UI indicator instead of showing
          // intrusive error messages
          setState(() {
            _audioError = true;
            _audioErrorMsg = "Unable to play audio";
          });

          // Log for debugging only
          final errorMsg =
              'Error playing audio for character: ${_currentCharacter.character}, '
              'Path: ${_currentCharacter.audioAssetPath}';
          debugPrint(errorMsg);
        }
      }
    }
  }

  /// Starts a new learning round.
  ///
  /// This method is called at the beginning of the learning session and
  /// after each correct answer (if the session is not finished).
  /// It retrieves the next character and choices from [_learnAdapter],
  /// sets up the UI choices, handles stage transition animations,
  /// and resets feedback states.
  void _startNewRound() {
    debugPrint("--- _startNewRound Called ---");

    // Get choices for this round
    List<HskCharacter> roundChoices = _learnAdapter.startNewRound();

    // Get the current character to learn
    _currentCharacter = _learnAdapter.getCurrentCharacter();
    debugPrint(
        "Current Character to Learn: ${_currentCharacter.character} (Pinyin: ${_currentCharacter.pinyin}, English: ${_currentCharacter.englishTranslation}, ID: ${_currentCharacter.characterId})");

    debugPrint(
        "Raw Round Choices from JavaLearnAdapter (count: ${roundChoices.length}):");
    for (int i = 0; i < roundChoices.length; i++) {
      final choice = roundChoices[i];
      debugPrint(
          "  Choice ${i + 1}: ${choice.character} (Pinyin: ${choice.pinyin}, English: ${choice.englishTranslation}, ID: ${choice.characterId})");
    }

    // Set up choices based on the display type
    _setupChoices(roundChoices);

    // Handle animations for stage transitions
    _handleStageTransition();

    // Test reset logic for critical stages
    _checkResetLogicForStage();

    setState(() {
      _showingFeedback = false;
      _selectedChoiceIndex = -1;
      _incorrectChoices = {}; // Reset incorrect choices for the new round
    });
  }

  // Handles animations for transitions between different stages
  /// Manages UI animations when transitioning between learning stages
  /// (e.g., when the number of choices changes).
  /// It updates [_isAnimating] and calls specific animation methods.
  void _handleStageTransition() {
    int currentDisplaySize = _learnAdapter.numberOfChoices;

    if (_previousDisplaySize != currentDisplaySize &&
        _previousDisplaySize > 0) {
      setState(() {
        _isAnimating = true;
      });

      // Similar to the Java version's transition logic
      if (_previousDisplaySize == 1 && currentDisplaySize == 3) {
        _animateOneToThree();
      } else if (_previousDisplaySize == 3 && currentDisplaySize == 1) {
        _animateThreeToOne();
      } else if (_previousDisplaySize == 3 && currentDisplaySize == 5) {
        _animateThreeToFive();
      } else if (_previousDisplaySize == 5 && currentDisplaySize == 1) {
        _animateFiveToOne();
      } else if (_previousDisplaySize == 5 && currentDisplaySize == 8) {
        _animateFiveToEight();
      } else if (_previousDisplaySize == 8 && currentDisplaySize == 1) {
        _animateEightToOne();
      }

      _previousDisplaySize = currentDisplaySize;
    } else if (_previousDisplaySize == 0) {
      // First time, just set the size without animation
      _previousDisplaySize = currentDisplaySize;
    }
  }

  // Animation from 1 button to 3 buttons
  void _animateOneToThree() {
    _currentTransition = "one_to_three";
    _animatingButtonIndex = 0;

    // Track position history like in Java
    _lastDisplayPos = 0;

    // Create controller for this specific transition
    _transitionController.duration = _kOneThreeTransitionDuration;
    _transitionController.reset();

    // Increment learn progress (matching Java behavior)
    _incrementProgress();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    // Start animation
    _transitionController.forward();
  }

  // Animation from 3 buttons to 1 button
  void _animateThreeToOne() {
    _currentTransition = "three_to_one";
    _animatingButtonIndex = 0;

    // Track position based on last displayed item
    _lastDisplayPos = _learnAdapter.getPositionShuffle().indexOf(0);

    // Different animation based on which button was last displayed
    _transitionController.duration = _kOneThreeTransitionDuration;
    _transitionController.reset();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    _transitionController.forward();
  }

  // Animation from 3 buttons to 5 buttons
  void _animateThreeToFive() {
    _currentTransition = "three_to_five";

    // Track position for animation
    _lastDisplayPos = _learnAdapter.getPositionShuffle().indexOf(0);

    // Transition speed matching Java
    _transitionController.duration = _kThreeFiveTransitionDuration;
    _transitionController.reset();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    _transitionController.forward();
  }

  // Animation from 5 buttons to 1 button
  void _animateFiveToOne() {
    _currentTransition = "five_to_one";
    _animatingButtonIndex = 0;

    // Track position for animation
    _lastDisplayPos = _learnAdapter.getPositionShuffle().indexOf(0);

    _transitionController.duration = _kThreeFiveTransitionDuration;
    _transitionController.reset();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    _transitionController.forward();
  }

  // Animation from 5 buttons to 8 buttons
  void _animateFiveToEight() {
    _currentTransition = "five_to_eight";

    // Track position for animation
    _lastDisplayPos = _learnAdapter.getPositionShuffle().indexOf(0);

    _transitionController.duration = _kFiveEightTransitionDuration;
    _transitionController.reset();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    _transitionController.forward();
  }

  // Animation from 8 buttons to 1 button
  void _animateEightToOne() {
    _currentTransition = "eight_to_one";
    _animatingButtonIndex = 0;

    // Track position for animation
    _lastDisplayPos = _learnAdapter.getPositionShuffle().indexOf(0);

    _transitionController.duration = _kFiveEightTransitionDuration;
    _transitionController.reset();

    // Add haptic feedback for transition
    HapticFeedback.lightImpact();

    _transitionController.forward();
  }

  // Helper method to increment progress (similar to Java's incLearnProgress)
  void _incrementProgress() {
    // In Java, this increments a progress counter in the database
    // For now, we'll just log it
    debugPrint("Incrementing learn progress");

    // TODO: Implement actual progress incrementing when database is added
  }

  /// Sets up the [_choices] list for the UI based on the [roundChoices]
  /// from [_learnAdapter] and the current `displayType`.
  ///
  /// This determines what content (character, pinyin, English) is displayed
  /// on the answer buttons.
  void _setupChoices(List<HskCharacter> roundChoices) {
    debugPrint(
        "--- _setupChoices Called (Display Type: ${_learnAdapter.displayType}) ---");
    // Check if we're in the final stage
    if (_learnAdapter.isFinalStage) {
      // Final stage - confirmation mode
      _setupFinalStageChoices(roundChoices);
      return;
    }

    int displayType = _learnAdapter.displayType;

    if (displayType == 0) {
      // Chinese as prompt, pinyin/English as answers
      _setupPinyinChoices(roundChoices);
    } else if (displayType == 1) {
      // Pinyin/English as prompt, Chinese as answers
      _setupCharacterChoices(roundChoices);
    } else {
      // Special mode (type 2)
      _setupSpecialModeChoices(roundChoices);
    }
    debugPrint("Processed _choices for UI (count: ${_choices.length}):");
    for (int i = 0; i < _choices.length; i++) {
      final choiceItem = _choices[i];
      if (choiceItem is HskCharacter) {
        debugPrint(
            "  UI Choice ${i + 1} (HskCharacter): ${choiceItem.character} (Pinyin: ${choiceItem.pinyin}, English: ${choiceItem.englishTranslation})");
      } else if (choiceItem is Map<String, dynamic>) {
        debugPrint(
            "  UI Choice ${i + 1} (Map): Pinyin: ${choiceItem['pinyin']}, English: ${choiceItem['english']}");
      } else {
        debugPrint(
            "  UI Choice ${i + 1} (Unknown type): ${choiceItem.toString()}");
      }
    }
    debugPrint("-----------------------------------");
  }

  void _setupPinyinChoices(List<HskCharacter> characters) {
    _choices = characters
        .map((char) => {
              'pinyin': char.pinyin,
              'english': char.englishTranslation,
            })
        .toList();
  }

  void _setupCharacterChoices(List<HskCharacter> characters) {
    _choices = characters;
  }

  void _setupSpecialModeChoices(List<HskCharacter> characters) {
    // Special mode for stages 8 and 9
    if (characters.isNotEmpty) {
      if (_learnAdapter.displayType == 0) {
        // Show pinyin/English
        _choices = [
          {
            'pinyin': characters[0].pinyin,
            'english': characters[0].englishTranslation,
          }
        ];
      } else {
        // Show character
        _choices = [characters[0]];
      }
    }
  }

  void _setupFinalStageChoices(List<HskCharacter> characters) {
    // Final stage - confirmation mode
    if (characters.isNotEmpty) {
      // For final stage, we always want to show the Chinese character on the button
      // regardless of display type
      _choices = [_currentCharacter];

      // Debug log to verify what's happening
      debugPrint(
          'Final stage setup: displayType=${_learnAdapter.displayType}, ' +
              'character=${_currentCharacter.character}');
    }
  }

  /// Handles the event when a user selects an answer choice.
  ///
  /// It checks if the answer is correct, plays feedback animations
  /// and haptics, updates learning progress via [_learnAdapter],
  /// saves progress if needed, and either starts a new round or ends the session.
  ///
  /// [index]: The index of the chosen answer in the [_choices] list.
  void _handleChoiceSelected(int index) async {
    await ref.read(hskLearnSettingsNotifierProvider.future);

    setState(() {
      _selectedChoiceIndex = index;
      _showingFeedback = true;
    });

    // Check if the answer is correct
    int correctIndex = _learnAdapter.getCorrectAnswerIndex();
    bool isCorrect = index == correctIndex;

    // Play haptic feedback for user interaction
    HapticFeedback.selectionClick();

    if (isCorrect) {
      // Show correct animation
      setState(() {
        _showingCorrectAnimation = true;
      });
      _correctAnswerController.reset();
      _correctAnswerController.forward();

      // Slight delay to show animation
      await Future.delayed(_kFeedbackAnimationDelay);

      // Handle correct answer
      _learnAdapter.handleCorrectAnswer();

      // Check if we should save progress
      if (_learnAdapter.checkSaveNow()) {
        // Save progress in background
        final savePairs = _learnAdapter.getSaveNowPairs();
        BackgroundSaveHelper.saveProgress(savePairs);
      }

      // Increment character view count
      Future.microtask(() {
        ref
            .read(characterProgressProvider.notifier)
            .incrementViewCount(_currentCharacter);
      });

      // Check if the learning session is finished
      if (_learnAdapter.isFinished) {
        // End the session
        _endSession();
      } else {
        // Start a new round
        _startNewRound();
        _playAudio();
      }
    } else {
      // Show wrong animation
      setState(() {
        _showingWrongAnimation = true;
      });
      _wrongAnswerController.reset();
      _wrongAnswerController.forward();

      // Slight delay to show animation
      await Future.delayed(_kFeedbackAnimationDelay);

      // Handle wrong answer
      _learnAdapter.handleWrongAnswer();
      _missesCount++;

      // Mark this choice as incorrect so it can be hidden
      setState(() {
        _incorrectChoices.add(index); // Add to the set of incorrect choices
        _showingFeedback = false;
        _selectedChoiceIndex = -1;
        _showingWrongAnimation = false;
      });
    }
  }

  // End screen state
  bool _isShowingEndScreen = false;

  /// Ends the current learning session.
  ///
  /// Displays "The End" message, plays a completion sound, and
  /// navigates to the [HskLearnCompleteScreen] after a delay.
  void _endSession() {
    // Show "The End" on the main text screen (matching Java version)
    setState(() {
      _choices = [];
      _isShowingEndScreen = true;
    });

    // Play completion sound
    try {
      _audioPlayer.play(AssetSource('audio/completion.mp3'));

      // Add haptic feedback for completion
      HapticFeedback.heavyImpact();

      // Subtle delay to let haptic finish
      Future.delayed(_kHapticFeedbackDelay, () {
        HapticFeedback.mediumImpact();
      });
    } catch (e) {
      // Ignore errors with completion sound
      debugPrint('Could not play completion sound: $e');
    }

    // Delay exit like in the Java version
    Future.delayed(_kEndSessionDelay, () {
      // Calculate session duration
      final sessionDuration = ref
          .read(hskSessionProgressProvider.notifier)
          .getSessionDurationSeconds();

      // Navigate to completion screen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => HskLearnCompleteScreen(
              duration: sessionDuration,
              misses: _missesCount,
              characterSet: widget.characterSet,
            ),
          ),
        );
      }
    });
  }

  // Verify reset logic to ensure proper stage transitions
  void _checkResetLogicForStage() {
    int currentStage = _learnAdapter.currentStage;

    // Log key stage transitions for debugging
    if (currentStage != _previousDisplaySize && currentStage > 0) {
      debugPrint('Stage transition: $_previousDisplaySize -> $currentStage');

      // Get detailed reset logic state
      final resetLogicState = _learnAdapter.getResetLogicState();

      // Log critical stage changes
      if (currentStage == 4) {
        final shouldReset = _learnAdapter.shouldResetAtStageFour();
        debugPrint(
            'Stage 4 - Reset check: shouldReset=$shouldReset, learnedItems=${resetLogicState['learnItemsCount']}');
      }

      // Check stage 7 which has special conditions in Java
      if (currentStage == 7) {
        final shouldReset = _learnAdapter.shouldResetAtStageSeven();
        debugPrint(
            'Stage 7 - Reset check: shouldReset=$shouldReset, learnedItems=${resetLogicState['learnItemsCount']}');
        debugPrint(
            'Stage 7 - Bool1=${resetLogicState['stageSevenBool1']}, Bool2=${resetLogicState['stageSevenBool2']}');
      }

      // Stage 8 is the final learning stage before review
      if (currentStage == 8) {
        debugPrint(
            'Stage 8 - Moving to final learning stage with ${resetLogicState['learnItemsCount']} learned items');
      }

      // Stage 9 is the review stage
      if (currentStage == 9) {
        debugPrint(
            'Stage 9 - Moving to review stage with ${resetLogicState['learnItemsCount']} learned items');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get theme-aware HSK colors and typography
    final hskColors = ColorSystem.getHSKColors(context);
    final hskTypography = AppTypography.getHSKStyles(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Learn Mode",
          style: hskTypography.screenTitle,
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          IconButton(
            icon: const Icon(AppIcons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: Container(
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Column(
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: (_learnAdapter.currentStage) /
                    _kTotalLearningStages, // 9 stages total
                backgroundColor: hskColors.progressBackground,
                valueColor: AlwaysStoppedAnimation<Color>(
                  hskColors.progressValue,
                ),
              ),

              // Stage indicator with consistent padding
              Padding(
                padding: EdgeInsets.symmetric(vertical: DesignSystem.spaceXS),
                child: Text(
                  'Stage ${_learnAdapter.currentStage}/$_kTotalLearningStages',
                  style: HskTypography.stageIndicator,
                ),
              ),

              // Main content with proper layout management
              Expanded(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    DesignSystem.spaceM, // 16.0 -> standardized
                    DesignSystem.spaceS, // 8.0 -> standardized
                    DesignSystem.spaceM, // 16.0 -> standardized
                    DesignSystem.spaceM, // 16.0 -> standardized
                  ),
                  child: Column(
                    children: [
                      // Prompt area with intrinsic height instead of expanded
                      Padding(
                        padding: EdgeInsets.only(bottom: DesignSystem.spaceS),
                        child: _buildPromptArea(),
                      ),

                      // No extra spacer since we want buttons to start immediately after prompt

                      // Answer area in flexible container that adapts to content
                      // Removed vertical centering to keep buttons starting from top
                      Expanded(
                        child: _buildAnswerArea(),
                      ),
                    ],
                  ),
                ),
              ),

              // Mountain silhouette decoration - Matching HSK Set Details design
              Container(
                height: 100,
                width: double.infinity,
                alignment: Alignment.bottomCenter,
                child: const MountainDecoration(height: 80),
              ),

              // Audio button positioned at bottom center, more compact
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: EdgeInsets.only(
                    right: DesignSystem
                        .spaceM, // 16.0 -> 12.0 (preserves compact spacing)
                    bottom:
                        DesignSystem.spaceXS, // 4.0 (preserves exact spacing)
                  ),
                  child: _buildAudioButton(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the prompt area of the screen.
  ///
  /// This area displays the current character, pinyin/English prompt,
  /// or special messages like "The End" or final stage congratulations.
  /// Its content is determined by the current learning stage and settings.
  Widget _buildPromptArea() {
    return FutureBuilder<HskLearnSettings>(
      future: ref.read(hskLearnSettingsNotifierProvider.future),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox(
              height: 100, child: Center(child: CircularProgressIndicator()));
        }

        final settings = snapshot.data!;
        final displayType = _learnAdapter.displayType;
        final hskColors = ColorSystem.getHSKColors(context);

        // Check if showing end screen
        if (_isShowingEndScreen) {
          return Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal:
                  DesignSystem.spaceXL, // 32.0 (preserves exact spacing)
              vertical: DesignSystem.spaceL, // 24.0 (preserves exact spacing)
            ),
            decoration: BoxDecoration(
              color: hskColors.appBarBackground,
              borderRadius: BorderRadius.circular(_kPromptBorderRadius),
            ),
            child: const Center(
              child: Text(
                "The End",
                style: HskTypography.endSession,
              ),
            ),
          );
        }

        // Check if we're in the final stage
        if (_learnAdapter.isFinalStage) {
          return Container(
            width: double.infinity,
            constraints: const BoxConstraints(
              minHeight: _kPromptMinHeight,
              maxHeight: _kPromptMaxHeight,
            ),
            padding: _kPromptPadding,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .surfaceContainerHighest
                  .withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(_kPromptBorderRadius),
            ),
            child: _buildFinalStagePrompt(settings, displayType),
          );
        }

        // For display type 0, show character if text prompt is enabled
        // For display type 1, show pinyin/English if text prompt is enabled
        Widget promptContent;

        if (displayType == 0 && settings.showTextPrompt) {
          // Show character as prompt
          promptContent = Text(
            _currentCharacter.character,
            style: HskTypography.chineseCharacterLarge.copyWith(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ),
          );
        } else if (displayType == 1 && settings.showTextPrompt) {
          // Show pinyin/English as prompt with improved overflow handling
          promptContent = Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (settings.showPinyinPrompt)
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    _currentCharacter.pinyin,
                    style: HskTypography.pinyinMedium.copyWith(
                      color: DesignSystem.getSettingsTextColor(context,
                          isPrimary: true),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              if (settings.showPinyinPrompt && settings.showEnglishTranslation)
                SizedBox(
                    height:
                        DesignSystem.spaceXS), // 4.0 (preserves exact spacing)
              if (settings.showEnglishTranslation)
                Flexible(
                  child: Text(
                    _currentCharacter.englishTranslation,
                    style: HskTypography.englishMedium.copyWith(
                      color: DesignSystem.getSettingsTextColor(context,
                          isPrimary: false),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2, // Limit to 2 lines
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          );
        } else {
          // Placeholder for when text prompt is disabled
          promptContent = Icon(
            Icons.help_outline,
            size: 64,
            color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
          );
        }

        return Container(
          width: double.infinity,
          constraints: const BoxConstraints(
            minHeight: 80, // Minimum height
            maxHeight: 130, // Slightly increased to prevent overflow
          ),
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceL, // 20.0 (preserves exact spacing)
            vertical: DesignSystem.spaceM - 4, // 12.0 (preserves exact spacing)
          ),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .surfaceContainerHighest
                .withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(DesignSystem.radiusM), // 16.0
          ),
          child: Stack(
            children: [
              Center(child: promptContent),

              // Subtle audio error indicator
              if (_audioError)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Tooltip(
                    message: _audioErrorMsg,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: hskColors.audioError,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        AppIcons.audioOff,
                        color: DesignSystem.getSettingsTextColor(context,
                            isPrimary: true),
                        size: 16,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinalStagePrompt(HskLearnSettings settings, int displayType) {
    Widget promptContent;

    if (displayType == 0) {
      // Show character as prompt in final stage
      promptContent = Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            // Allow FittedBox to take available space
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(
                _currentCharacter.character,
                style: HskTypography.withSize(
                  HskTypography.chineseCharacterLarge,
                  _kFinalPromptCharSize,
                ),
              ),
            ),
          ),
          SizedBox(height: _kFinalPromptSpacing1),
          FittedBox(
            // Ensure "Congratulations!" fits
            fit: BoxFit.scaleDown,
            child: Text(
              'Congratulations!',
              style: TextStyle(
                fontSize: _kFinalPromptCongratsFontSize,
                color:
                    DesignSystem.getSettingsTextColor(context, isPrimary: true),
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansSC',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: _kFinalPromptSpacing2),
          Text(
            'Tap the button below to continue',
            style: TextStyle(
              fontSize: _kFinalPromptSubTextFontSize,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
              fontFamily: 'NotoSansSC',
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      );
    } else {
      // Show pinyin/English as prompt in final stage
      promptContent = Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (settings.showPinyinPrompt)
            Expanded(
              // Allow FittedBox for pinyin to take available space
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  _currentCharacter.pinyin,
                  style: TextStyle(
                    fontSize: _kFinalPromptPinyinFontSize,
                    color: DesignSystem.getSettingsTextColor(context,
                        isPrimary: true),
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NotoSansSC',
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          if (settings.showPinyinPrompt && settings.showEnglishTranslation)
            SizedBox(height: _kFinalPromptTextSpacing),
          if (settings.showEnglishTranslation)
            Expanded(
              // Allow Flexible for English to take available space
              child: FittedBox(
                // Also use FittedBox for English
                fit: BoxFit.scaleDown,
                child: Text(
                  _currentCharacter.englishTranslation,
                  style: TextStyle(
                    fontSize: _kFinalPromptEnglishFontSize,
                    color: DesignSystem.getSettingsTextColor(context,
                        isPrimary: false),
                    fontFamily: 'NotoSansSC',
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2, // Keep maxLines for English if desired
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          SizedBox(height: _kFinalPromptSpacing1), // Consistent spacing
          FittedBox(
            // Ensure "Congratulations!" fits
            fit: BoxFit.scaleDown,
            child: Text(
              'Congratulations!',
              style: TextStyle(
                fontSize: _kFinalPromptCongratsFontSize,
                color:
                    DesignSystem.getSettingsTextColor(context, isPrimary: true),
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansSC',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: _kFinalPromptSpacing2), // Consistent spacing
          Text(
            'Tap the button below to continue',
            style: TextStyle(
              fontSize: _kFinalPromptSubTextFontSize,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
              fontFamily: 'NotoSansSC',
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      );
    }

    // The Container wrapper was removed from here.
    // _buildPromptArea now directly uses the Column (promptContent).
    return promptContent;
  }

  /// Builds the area displaying the answer choice buttons.
  ///
  /// It uses a [LayoutBuilder] to create an adaptive grid of buttons
  /// (1, 2, or 3 rows). The content and appearance of buttons depend on
  /// the learning stage, display type, and user settings.
  Widget _buildAnswerArea() {
    return FutureBuilder<HskLearnSettings>(
      future: ref.read(hskLearnSettingsNotifierProvider.future),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        final settings = snapshot.data!;
        final displayType = _learnAdapter.displayType;
        final itemCount = _choices.length;

        // Create better adaptive layout
        return LayoutBuilder(builder: (context, constraints) {
          final double availableWidthForGrid = constraints.maxWidth;
          final double availableHeightForGrid = constraints.maxHeight;
          double targetButtonWidth;
          double targetButtonHeight;

          // Determine effective items per row for sizing strategy
          int effectiveItemsPerRow;
          if (itemCount == 0) {
            effectiveItemsPerRow = 1; // Avoid division by zero, placeholder
          } else if (itemCount == 1) {
            effectiveItemsPerRow = 1;
          } else if (itemCount == 2) {
            effectiveItemsPerRow = 2;
          } else {
            // 3 or more items, assume a max of 3 for width calculation for consistency
            effectiveItemsPerRow = 3;
          }

          // Calculate targetButtonWidth based on effectiveItemsPerRow
          // Add some horizontal padding/gap factor
          double totalHorizontalGapFactor;
          if (itemCount == 1) {
            totalHorizontalGapFactor =
                0.35; // Single button can be smaller than full width
          } else if (effectiveItemsPerRow == 2) {
            totalHorizontalGapFactor = (effectiveItemsPerRow * 0.05) +
                0.05; // Aim for ~85% utilization
          } else {
            totalHorizontalGapFactor = (effectiveItemsPerRow * 0.04) +
                0.04; // Aim for ~88% utilization
          }

          targetButtonWidth = (availableWidthForGrid / effectiveItemsPerRow) *
              (1 - totalHorizontalGapFactor);
          targetButtonWidth =
              targetButtonWidth.clamp(_kMinButtonWidth, _kMaxButtonWidth);

          // Calculate vertical layout based on number of rows needed
          int rows = itemCount <= 3 ? 1 : (itemCount <= 6 ? 2 : 3);

          // Adjust button height based on available height and row count
          // Maximize usage of vertical space while ensuring no overflow
          double maxHeightPerRow = (availableHeightForGrid * 0.85) /
              rows; // Reduced to 85% for safety
          targetButtonHeight =
              maxHeightPerRow * 0.9; // Reduced to 90% to ensure no overflow

          // Clamp button height
          targetButtonHeight =
              targetButtonHeight.clamp(_kMinButtonHeight, _kMaxButtonHeight);

          // Ensure proper button aspect ratio
          if (itemCount == 1 && targetButtonWidth < targetButtonHeight * 0.8) {
            targetButtonWidth = targetButtonHeight * 0.8;
          }

          // Use minimal spacing for multi-row layouts to prevent overflow
          double rowSpacing = rows > 1
              ? DesignSystem.spaceXS // 4.0 (preserves exact spacing)
              : DesignSystem.spaceXS + 2; // 6.0 (preserves exact spacing)

          return Padding(
            padding: EdgeInsets.symmetric(
              horizontal:
                  DesignSystem.spaceM - 4, // 12.0 (preserves exact spacing)
              vertical: DesignSystem.spaceS, // 8.0 (preserves exact spacing)
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start, // Align from top
              crossAxisAlignment:
                  CrossAxisAlignment.center, // Center horizontally
              children: [
                // Determine counts for each row for clarity
                if (itemCount > 0) ...[
                  _buildButtonRow(0, min(3, itemCount), settings, displayType,
                      targetButtonWidth, targetButtonHeight),
                ],
                if (itemCount > 3) ...[
                  SizedBox(height: rowSpacing),
                  _buildButtonRow(3, min(3, itemCount - 3), settings,
                      displayType, targetButtonWidth, targetButtonHeight),
                ],
                if (itemCount > 6) ...[
                  SizedBox(height: rowSpacing),
                  _buildButtonRow(6, min(3, itemCount - 6), settings,
                      displayType, targetButtonWidth, targetButtonHeight),
                ],
              ],
            ),
          );
        });
      },
    );
  }

  /// Builds a single row of answer buttons.
  ///
  /// [startIndex]: The starting index in the `_choices` list for this row.
  /// [count]: The number of buttons in this row.
  /// [settings]: Current learning settings.
  /// [displayType]: Current display type from `_learnAdapter`.
  /// [targetWidth]: Calculated target width for each button.
  /// [targetHeight]: Calculated target height for each button.
  Widget _buildButtonRow(int startIndex, int count, HskLearnSettings settings,
      int displayType, double targetWidth, double targetHeight) {
    if (count <= 0)
      return const SizedBox.shrink(); // Do not build row if no items
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        final buttonIndex = startIndex + index;
        return _buildButton(
            buttonIndex, settings, displayType, targetWidth, targetHeight);
      }),
    );
  }

  Color _getButtonHighlightColor(int index, bool isPressed) {
    final hskColors = ColorSystem.getHSKColors(context);

    // Base semi-transparent color for highlights, can be adjusted
    Color baseHighlight = hskColors.buttonHighlight;
    if (isPressed) {
      baseHighlight = hskColors.buttonPressed;
    }

    if (_selectedChoiceIndex == index) {
      if (_showingCorrectAnimation &&
          index == _learnAdapter.getCorrectAnswerIndex()) {
        return Color.lerp(
          hskColors.correctStart, // Lighter green for selection start
          hskColors.correctEnd, // Stronger highlight
          _correctAnswerController.value,
        )!;
      } else if (_showingWrongAnimation) {
        return Color.lerp(
          hskColors.wrongStart, // Lighter red
          hskColors.wrongEnd,
          _wrongAnswerController.value,
        )!;
      } else {
        // Standard selection highlight (when just tapped)
        return hskColors.buttonSelection;
      }
    }
    return baseHighlight; // Default base if not selected or animated
  }

  /// Builds a single answer button.
  ///
  /// [index]: The index of the choice for this button.
  /// [settings]: Current learning settings.
  /// [displayType]: Current display type from `_learnAdapter`.
  /// [targetWidth]: Calculated target width for the button.
  /// [targetHeight]: Calculated target height for the button.
  Widget _buildButton(int index, HskLearnSettings settings, int displayType,
      double targetWidth, double targetHeight) {
    final bool isIncorrectAndHidden = _incorrectChoices.contains(index);

    // 1. Determine buttonContent and its style (white text, increased size)
    Widget buttonContent;
    // Using HskTypography system for consistent font family

    if (displayType == 0 && _choices[index] is Map<String, dynamic>) {
      // Display Pinyin and/or English for type 0 (Character prompt, Pinyin/English answers)
      buttonContent = ResponsiveButtonText(
        pinyin: _choices[index]['pinyin'],
        english: _choices[index]['english'],
        showPinyin: settings.showPinyinOnButtons,
        showEnglish: settings.showEnglishTranslation,
        maxWidth: targetWidth - _kButtonContentPadding.horizontal,
        maxHeight: targetHeight - _kButtonContentPadding.vertical,
      );
    } else if ((displayType == 1 && _choices[index] is HskCharacter) ||
        _learnAdapter.isFinalStage) {
      // Display Character for type 1 (Pinyin/English prompt, Character answers) or final stage
      final character = _learnAdapter.isFinalStage
          ? _currentCharacter.character
          : (_choices[index] as HskCharacter).character;
      buttonContent = ResponsiveButtonText(
        character: character,
        maxWidth: targetWidth - _kButtonContentPadding.horizontal,
        maxHeight: targetHeight - _kButtonContentPadding.vertical,
      );
    } else {
      buttonContent = Icon(Icons.question_mark,
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
          size: _kCharFontSize);
    }

    Widget buttonVisualCore;
    bool isCurrentlyAnimatedTransitionButton = _isAnimating &&
        ((_currentTransition == "one_to_three" && (index == 0 || index > 0)) ||
            (_currentTransition == "three_to_one" && index == 0) ||
            (_currentTransition == "three_to_five") ||
            (_currentTransition == "five_to_one" && index == 0) ||
            (_currentTransition == "five_to_eight") ||
            (_currentTransition == "eight_to_one" && index == 0));

    if (isCurrentlyAnimatedTransitionButton) {
      Widget animatedContentContainer = Container(
        width: targetWidth, // Use passed target size
        height: targetHeight, // Use passed target size
        decoration: BoxDecoration(
          color: _buttonGlassColor, // Base glass color for transitions
          border: Border.all(color: _buttonBorderColor, width: 0.5),
          borderRadius: BorderRadius.circular(
              _kButtonBorderRadius), // ensure radius here too
        ),
        alignment:
            Alignment.center, // Ensures content (like Column) is centered
        padding: _kButtonContentPadding, // Padding around the content
        child: buttonContent, // Direct child, FittedBox removed from here
      );

      Widget animatedButton = InkWell(
        // Temporarily remove ClipRRect and BackdropFilter
        onTap: _showingFeedback || isIncorrectAndHidden
            ? null
            : () => _handleChoiceSelected(index),
        borderRadius: BorderRadius.circular(_kButtonBorderRadius),
        child: animatedContentContainer,
      );
      /*
      Widget animatedButton = ClipRRect(
        borderRadius: BorderRadius.circular(_kButtonBorderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
          child: InkWell(
            onTap: _showingFeedback || isIncorrectAndHidden ? null : () => _handleChoiceSelected(index),
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
            child: animatedContentContainer,
          ),
        ),
      );
      */

      // Apply specific transition animations
      if (_currentTransition == "one_to_three") {
        if (index == 0) {
          // First button scales up
          buttonVisualCore = AnimatedBuilder(
            animation:
                _scaleAnimation, // Assumes _scaleAnimation is 1.0 to targetScale
            builder: (context, child) =>
                Transform.scale(scale: _scaleAnimation.value, child: child),
            child: animatedButton,
          );
        } else {
          // New buttons fade in
          buttonVisualCore = AnimatedBuilder(
            animation: _fadeAnimation, // Assumes _fadeAnimation is 0.0 to 1.0
            builder: (context, child) =>
                Opacity(opacity: _fadeAnimation.value, child: child),
            child: animatedButton,
          );
        }
      } else if (_currentTransition == "three_to_one" && index == 0) {
        buttonVisualCore = AnimatedBuilder(
          animation:
              _transitionController, // Scale down slightly then back to normal
          builder: (context, child) => Transform.scale(
              scale: 1.0 - 0.1 * (1.0 - _transitionController.value),
              child: child), // Simple scale out
          child: animatedButton,
        );
      } else if (_currentTransition == "three_to_five" ||
          _currentTransition == "five_to_eight") {
        buttonVisualCore = AnimatedBuilder(
          animation: _transitionController,
          builder: (context, child) {
            double opacity = 1.0;
            double yOffset = 0;
            double scale = 1.0;

            if (index >= _previousDisplaySize) {
              // Newly added button
              opacity = _transitionController.value; // Fade in
              yOffset = 10 *
                  (1 - _transitionController.value); // Slide in from bottom
              scale = 0.8 + 0.2 * _transitionController.value; // Scale up
            } else {
              // Existing buttons
              yOffset =
                  -5 * _transitionController.value; // Shift slightly upward
              scale =
                  1.0 - 0.05 * _transitionController.value; // Slight scale down
            }
            return Opacity(
              opacity: opacity,
              child: Transform.translate(
                offset: Offset(0, yOffset),
                child: Transform.scale(scale: scale, child: child),
              ),
            );
          },
          child: animatedButton,
        );
      } else if ((_currentTransition == "five_to_one" ||
              _currentTransition == "eight_to_one") &&
          index == 0) {
        buttonVisualCore = AnimatedBuilder(
          animation: _transitionController,
          builder: (context, child) => Transform.scale(
              scale: 1.0 - 0.1 * (1.0 - _transitionController.value),
              child: child), // Simple scale out
          child: animatedButton,
        );
      } else {
        buttonVisualCore =
            animatedButton; // Fallback if transition logic is missed
      }
    } else {
      // Standard button appearance (not in active stage transition)
      Color currentButtonBgColor = _getButtonHighlightColor(index, false);

      buttonVisualCore = AnimatedScale(
        scale: _selectedChoiceIndex == index &&
                (_showingCorrectAnimation || _showingWrongAnimation)
            ? 1.05
            : 1.0,
        duration: _kButtonPulseDuration,
        child: Material(
          // Temporarily remove ClipRRect and BackdropFilter
          type: MaterialType.transparency,
          borderRadius: BorderRadius.circular(_kButtonBorderRadius),
          child: InkWell(
            onTap: _showingFeedback || isIncorrectAndHidden
                ? null
                : () => _handleChoiceSelected(index),
            onHighlightChanged: (isPressed) {
              // Potentially update color on highlight if needed, though _getButtonHighlightColor handles selection
            },
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
            splashColor: _buttonSplashColor,
            highlightColor: _buttonHighlightColor,
            child: Container(
              width: targetWidth, // Use passed target size
              height: targetHeight, // Use passed target size
              decoration: BoxDecoration(
                color:
                    currentButtonBgColor, // Applied to the container over the blur
                border: Border.all(color: _buttonBorderColorStrong, width: 1.0),
                borderRadius: BorderRadius.circular(
                    _kButtonBorderRadius), // Ensure radius here for the border to be rounded
              ),
              alignment:
                  Alignment.center, // Ensures content (like Column) is centered
              padding: _kButtonContentPadding, // Padding around the content
              clipBehavior:
                  Clip.hardEdge, // Ensure content is clipped to bounds
              child: buttonContent, // Direct child, FittedBox removed from here
            ),
          ),
        ),
      );
    }

    return Opacity(
      opacity: isIncorrectAndHidden ? 0.0 : 1.0,
      child: IgnorePointer(
        ignoring: isIncorrectAndHidden,
        child: Padding(
          padding: _kButtonPadding,
          child: buttonVisualCore,
        ),
      ),
    );
  }

  /// Builds the floating action button for replaying audio.
  Widget _buildAudioButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 4.0),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main audio button
          Material(
            color: Theme.of(context).colorScheme.primary,
            shape: const CircleBorder(),
            elevation: 3.0,
            child: InkWell(
              onTap: _playAudio,
              customBorder: const CircleBorder(),
              child: Padding(
                padding: const EdgeInsets.all(8.0), // Reduced padding
                child: Icon(
                  Icons.volume_up,
                  color: _audioError
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.onPrimary,
                  size: 28, // Slightly smaller
                ),
              ),
            ),
          ),

          // Subtle error indicator
          if (_audioError)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 12, // Slightly smaller
                height: 12, // Slightly smaller
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 8, // Slightly smaller
                  color: Theme.of(context).colorScheme.onError,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Shows the settings dialog.
  ///
  /// Allows the user to modify learning preferences like auto-play sound,
  /// prompt visibility, etc. Changes are saved via [hskLearnSettingsNotifierProvider].
  void _showSettingsDialog(BuildContext context) async {
    final currentSettings =
        await ref.read(hskLearnSettingsNotifierProvider.future);

    // ignore: use_build_context_synchronously
    showDialog(
      context: context,
      builder: (context) => LearnSettingsDialog(
        initialSettings: currentSettings,
        onSettingsChanged: (newSettings) {
          // Save settings via provider
          ref
              .read(hskLearnSettingsNotifierProvider.notifier)
              .updateSettings(newSettings);

          // Immediately apply settings to current UI
          _applySettingsChanges(currentSettings, newSettings);
        },
      ),
    );
  }

  // Apply settings changes immediately to match Java behavior
  void _applySettingsChanges(
      HskLearnSettings oldSettings, HskLearnSettings newSettings) {
    // Check which settings have changed and update UI accordingly
    bool needsUIRefresh = false;

    // Handle audio playback
    if (!oldSettings.autoPlaySound && newSettings.autoPlaySound) {
      _playAudio();
    }

    // Force UI refresh if needed for visual elements
    if (oldSettings.showTextPrompt != newSettings.showTextPrompt ||
        oldSettings.showPinyinPrompt != newSettings.showPinyinPrompt ||
        oldSettings.showEnglishTranslation !=
            newSettings.showEnglishTranslation ||
        oldSettings.showPinyinOnButtons != newSettings.showPinyinOnButtons) {
      needsUIRefresh = true;
    }

    if (needsUIRefresh) {
      // Force UI refresh to show new settings
      setState(() {
        // This will trigger rebuild with new settings
      });
    }
  }
}

/// A dialog for configuring HSK learn mode settings.
///
/// Allows users to toggle options such as auto-playing audio,
/// visibility of text prompts, pinyin, and English translations.
class LearnSettingsDialog extends StatefulWidget {
  /// The initial settings to display in the dialog.
  final HskLearnSettings initialSettings;

  /// Callback function invoked when settings are changed and applied.
  final void Function(HskLearnSettings) onSettingsChanged;

  /// Creates a [LearnSettingsDialog].
  const LearnSettingsDialog({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<LearnSettingsDialog> createState() => _LearnSettingsDialogState();
}

class _LearnSettingsDialogState extends State<LearnSettingsDialog> {
  late HskLearnSettings settings;

  @override
  void initState() {
    super.initState();
    settings = widget.initialSettings;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Learn Mode Settings"),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text("Auto Play Sound"),
              value: settings.autoPlaySound,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    autoPlaySound: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show Text Prompt"),
              value: settings.showTextPrompt,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showTextPrompt: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Delay Text Prompt"),
              value: settings.delayTextPrompt,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    delayTextPrompt: value ?? false,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show Pinyin in Prompt"),
              value: settings.showPinyinPrompt,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showPinyinPrompt: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show English in Prompt"),
              value: settings.showEnglishTranslation,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showEnglishTranslation: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show Pinyin on Buttons"),
              value: settings.showPinyinOnButtons,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showPinyinOnButtons: value ?? true,
                  );
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text("CANCEL"),
        ),
        TextButton(
          onPressed: () {
            widget.onSettingsChanged(settings);
            Navigator.of(context).pop();
          },
          child: const Text("OKAY"),
        ),
      ],
    );
  }
}

// Completion screen shown at the end of a learn session
/// A screen displayed upon completion of an HSK learning session.
///
/// It shows statistics like session duration and number of mistakes,
/// and provides options to return to the main menu or start a new session.
class HskLearnCompleteScreen extends ConsumerWidget {
  /// The duration of the completed session in seconds.
  final int duration;

  /// The number of mistakes made during the session.
  final int misses;

  /// The character set that was learned.
  final HskCharacterSet characterSet;

  /// Creates an [HskLearnCompleteScreen].
  const HskLearnCompleteScreen({
    super.key,
    required this.duration,
    required this.misses,
    required this.characterSet,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.secondaryContainer,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                // Title
                const Text(
                  "Session Complete",
                  style: HskTypography.completionTitle,
                ),
                const SizedBox(height: 20),
                // Subtitle
                const Text(
                  "You've completed this practice session. Tap the Return button to go back to the main menu or tap the Again button to practice again.",
                  textAlign: TextAlign.center,
                  style: HskTypography.completionDescription,
                ),
                const SizedBox(height: 40),
                // Stats panel
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .surfaceContainerHighest
                        .withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      _buildStatRow("Time", "${duration}s"),
                      const SizedBox(height: 8),
                      _buildStatRow("mistakes", "$misses"),
                      const SizedBox(height: 8),
                      _buildStatRow(
                          "charachter", "${characterSet.characters.length}"),
                    ],
                  ),
                ),
                const Spacer(),
                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Return button
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.outline,
                        foregroundColor: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 32, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        minimumSize: const Size(140, 50),
                      ),
                      child: const Text(
                        "Return",
                        style: HskTypography.buttonPrimary,
                      ),
                    ),
                    // Again button
                    ElevatedButton(
                      onPressed: () {
                        // Pop this screen
                        Navigator.pop(context);
                        // Start a new session with the same character set
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => HskLearnScreen(
                              characterSet: characterSet,
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.secondary,
                        foregroundColor: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 32, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        minimumSize: const Size(140, 50),
                      ),
                      child: const Text(
                        "Again",
                        style: HskTypography.buttonPrimary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: HskTypography.completionDescription,
        ),
        Text(
          value,
          style: HskTypography.completionDescription,
        ),
      ],
    );
  }
}
