import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/enums/reading_info.dart';
import 'package:dasso_reader/enums/text_selection_mode.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/models/bookmark.dart';
import 'package:dasso_reader/models/font_model.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/models/reading_rules.dart';
import 'package:dasso_reader/models/search_result_model.dart';
import 'package:dasso_reader/models/toc_item.dart';
import 'package:dasso_reader/page/book_player/image_viewer.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/providers/bookmark.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:dasso_reader/utils/coordinates_to_part.dart';
import 'package:dasso_reader/utils/js/convert_dart_color_to_js.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/context_menu/unified_context_menu.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/diagram.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/types_and_icons.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:intl/intl.dart';

class EpubPlayer extends ConsumerStatefulWidget {
  final Book book;
  final String? cfi;
  final Function showOrHideAppBarAndBottomBar;
  final Function onLoadEnd;
  final Function updateParent;

  const EpubPlayer(
      {super.key,
      required this.showOrHideAppBarAndBottomBar,
      required this.book,
      this.cfi,
      required this.onLoadEnd,
      required this.updateParent});

  @override
  ConsumerState<EpubPlayer> createState() => EpubPlayerState();
}

class EpubPlayerState extends ConsumerState<EpubPlayer>
    with TickerProviderStateMixin {
  late InAppWebViewController webViewController;
  late ContextMenu contextMenu;
  String cfi = '';
  double percentage = 0.0;
  String chapterTitle = '';
  String chapterHref = '';
  int chapterCurrentPage = 0;
  int chapterTotalPages = 0;
  List<TocItem> toc = [];
  OverlayEntry? contextMenuEntry;
  AnimationController? _animationController;
  Animation<double>? _animation;
  double searchProcess = 0.0;
  List<SearchResultModel> searchResult = [];
  bool showHistory = false;
  bool canGoBack = false;
  bool canGoForward = false;
  late Book book;
  String? backgroundColor;
  String? textColor;
  Timer? styleTimer;

  // Bookmark-related state
  bool bookmarkExists = false;
  String bookmarkCfi = '';

  final StreamController<double> _searchProgressController =
      StreamController<double>.broadcast();

  Stream<double> get searchProgressStream => _searchProgressController.stream;

  final StreamController<List<SearchResultModel>> _searchResultController =
      StreamController<List<SearchResultModel>>.broadcast();

  Stream<List<SearchResultModel>> get searchResultStream =>
      _searchResultController.stream;

  FocusNode focusNode = FocusNode();

  void prevPage() {
    webViewController.evaluateJavascript(source: 'prevPage()');
  }

  void nextPage() {
    webViewController.evaluateJavascript(source: 'nextPage()');
  }

  void prevChapter() {
    webViewController.evaluateJavascript(source: '''
      prevSection()
      ''');
  }

  void nextChapter() {
    // Use non-blocking JavaScript execution
    webViewController.evaluateJavascript(source: '''
      requestAnimationFrame(() => {
        nextSection();
      });
      ''');
  }

  Future<void> goToPercentage(double value) async {
    // Use non-blocking JavaScript execution with debouncing
    await webViewController.evaluateJavascript(source: '''
      if (window.goToPercentDebounce) clearTimeout(window.goToPercentDebounce);
      window.goToPercentDebounce = setTimeout(() => {
        goToPercent($value);
      }, 16); // 60fps debouncing
      ''');
  }

  void changeTheme(ReadTheme readTheme) {
    textColor = readTheme.textColor;
    backgroundColor = readTheme.backgroundColor;

    String bc = convertDartColorToJs(readTheme.backgroundColor);
    String tc = convertDartColorToJs(readTheme.textColor);

    // Use non-blocking theme change with requestAnimationFrame
    webViewController.evaluateJavascript(source: '''
      requestAnimationFrame(() => {
        changeStyle({
          backgroundColor: '#$bc',
          fontColor: '#$tc',
        });
      });
      ''');
  }

  void changeStyle(BookStyle bookStyle) {
    styleTimer?.cancel();
    styleTimer = Timer(DesignSystem.durationMedium, () {
      webViewController.evaluateJavascript(source: '''
      changeStyle({
        fontSize: ${bookStyle.fontSize},
        spacing: ${bookStyle.lineHeight},
        fontWeight: ${bookStyle.fontWeight},
        paragraphSpacing: ${bookStyle.paragraphSpacing},
        topMargin: ${bookStyle.topMargin},
        bottomMargin: ${bookStyle.bottomMargin},
        sideMargin: ${bookStyle.sideMargin},
        letterSpacing: ${bookStyle.letterSpacing},
        textIndent: ${bookStyle.indent},
        maxColumnCount: ${bookStyle.maxColumnCount},
      })
      ''');
    });
  }

  void changeReadingRules(ReadingRules readingRules) {
    webViewController.evaluateJavascript(source: '''
      readingFeatures({
        convertChineseMode: '${readingRules.convertChineseMode.name}',
        bionicReadingMode: ${readingRules.bionicReading},
      })
    ''');
  }

  void changeFont(FontModel font) {
    webViewController.evaluateJavascript(source: '''
      changeStyle({
        fontName: '${font.name}',
        fontPath: '${font.path}',
      })
    ''');
  }

  void changePageTurnStyle(PageTurn pageTurnStyle) {
    webViewController.evaluateJavascript(source: '''
      changeStyle({
        pageTurnStyle: '${pageTurnStyle.name}',
      })
    ''');
  }

  /// Set text selection mode in JavaScript
  void setTextSelectionMode(TextSelectionMode mode) {
    webViewController.evaluateJavascript(source: '''
      setTextSelectionMode('${mode.name}');
    ''');
  }

  void goToHref(String href) =>
      webViewController.evaluateJavascript(source: "goToHref('$href')");

  void goToCfi(String cfi) {
    webViewController.evaluateJavascript(source: "goToCfi('$cfi')");

    // Ensure annotations are visible after navigation
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        AnxLog.info(
            'Re-rendering annotations after CFI navigation to ensure highlights are visible');
        renderAnnotations(webViewController);
      }
    });
  }

  void addAnnotation(BookNote bookNote) {
    webViewController.evaluateJavascript(source: '''
      addAnnotation({
        id: ${bookNote.id},
        type: '${bookNote.type}',
        value: '${bookNote.cfi}',
        color: '#${bookNote.color}',
        note: '${bookNote.content.replaceAll('\n', ' ')}',
      })
      ''');
  }

  void removeAnnotation(String cfi) =>
      webViewController.evaluateJavascript(source: "removeAnnotation('$cfi')");

  // Bookmark-related JavaScript methods
  void addBookmarkHere() {
    webViewController.evaluateJavascript(source: "window.addBookmarkHere()");
  }

  void checkCurrentPageBookmark() {
    webViewController.evaluateJavascript(
        source: "window.checkCurrentPageBookmark()");
  }

  void showBookmarkIndicator() {
    webViewController.evaluateJavascript(
        source: "window.showBookmarkIndicator()");
  }

  void hideBookmarkIndicator() {
    webViewController.evaluateJavascript(
        source: "window.hideBookmarkIndicator()");
  }

  void removeBookmarkHere() {
    webViewController.evaluateJavascript(source: "window.removeBookmarkHere()");
  }

  void clearSearch() {
    webViewController.evaluateJavascript(source: "clearSearch()");
    searchResult.clear();
    _searchResultController.add(searchResult);
  }

  void search(String text) {
    clearSearch();
    webViewController.evaluateJavascript(source: '''
      search('$text', {
        'scope': 'book',
        'matchCase': false,
        'matchDiacritics': false,
        'matchWholeWords': false,
      })
    ''');
  }

  Future<void> initTts() async =>
      await webViewController.evaluateJavascript(source: "window.ttsHere()");

  void ttsStop() => webViewController.evaluateJavascript(source: "ttsStop()");

  Future<String> ttsNext() async => (await webViewController
          .callAsyncJavaScript(functionBody: "return await ttsNext()"))
      ?.value;

  Future<String> ttsPrev() async => (await webViewController
          .callAsyncJavaScript(functionBody: "return await ttsPrev()"))
      ?.value;

  Future<String> ttsPrevSection() async => (await webViewController
          .callAsyncJavaScript(functionBody: "return await ttsPrevSection()"))
      ?.value;

  Future<String> ttsNextSection() async => (await webViewController
          .callAsyncJavaScript(functionBody: "return await ttsNextSection()"))
      ?.value;

  Future<String> ttsPrepare() async =>
      (await webViewController.evaluateJavascript(source: "ttsPrepare()"));

  Future<bool> isFootNoteOpen() async => (await webViewController
      .evaluateJavascript(source: "window.isFootNoteOpen()"));

  void backHistory() {
    webViewController.evaluateJavascript(source: "back()");
  }

  void forwardHistory() {
    webViewController.evaluateJavascript(source: "forward()");
  }

  Future<String> theChapterContent() async =>
      await webViewController.evaluateJavascript(
        source: "theChapterContent()",
      );

  Future<String> previousContent(int count) async =>
      await webViewController.evaluateJavascript(
        source: "previousContent($count)",
      );

  // Auto-scroll functionality with performance optimization
  void startAutoScroll(double speed) {
    // Use non-blocking JavaScript execution with performance optimization
    webViewController.evaluateJavascript(
      source: '''
        requestAnimationFrame(() => {
          startAutoScroll($speed);
        });
      ''',
    );
  }

  void stopAutoScroll() {
    webViewController.evaluateJavascript(
      source: "stopAutoScroll()",
    );
  }

  void updateAutoScrollSpeed(double speed) {
    // Debounce speed updates to prevent excessive JavaScript calls
    webViewController.evaluateJavascript(
      source: '''
        if (window.speedUpdateDebounce) clearTimeout(window.speedUpdateDebounce);
        window.speedUpdateDebounce = setTimeout(() => {
          updateAutoScrollSpeed($speed);
        }, 16); // 60fps debouncing
      ''',
    );
  }

  Future<bool> isAutoScrollActive() async {
    final result = await webViewController.evaluateJavascript(
      source: "isAutoScrollActive()",
    );
    return result == true;
  }

  /// Preemptively process and cache visible Chinese content
  Future<void> preprocessVisibleContent() async {
    try {
      // Get visible text content from the current view
      final String visibleText =
          await webViewController.evaluateJavascript(source: '''
          (function() {
            try {
              // Get text from all visible paragraphs and other text elements
              const visibleElements = Array.from(document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6'))
                .filter(el => {
                  // Filter to elements that are visible and contain text
                  const rect = el.getBoundingClientRect();
                  return rect.top >= 0 &&
                         rect.left >= 0 &&
                         rect.bottom <= window.innerHeight &&
                         rect.right <= window.innerWidth &&
                         el.textContent.trim().length > 0;
                });

              return visibleElements.map(el => el.textContent).join('\\n\\n');
            } catch(e) {
              console.error('Error getting visible content:', e);
              return '';
            }
          })();
        ''');

      if (visibleText.isNotEmpty) {
        // Check if the text contains Chinese characters
        if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(visibleText)) {
          AnxLog.info(
              'Preprocessing visible Chinese content (${visibleText.length} chars)');

          // Get book ID for caching
          final bookId = widget.book.id;

          // Process in the background to avoid UI lag with lower priority
          Timer(const Duration(milliseconds: 100), () {
            unawaited(_preprocessChineseText(visibleText, bookId));
          });
        } else {
          AnxLog.info('No Chinese characters found in visible content');
        }
      }
    } catch (e) {
      AnxLog.warning('Error preprocessing visible content: $e');
    }
  }

  /// Process Chinese text in the background
  Future<void> _preprocessChineseText(String text, int bookId) async {
    // Split into manageable chunks to avoid processing huge blocks
    final chunks = _splitIntoChunks(text, 500); // ~500 chars per chunk

    final segmentationService = ChineseSegmentationService();

    // Process each chunk
    for (final chunk in chunks) {
      if (chunk.trim().isEmpty) continue;

      try {
        // This will automatically cache the results with the book ID
        await segmentationService.getWordBoundaries(chunk, bookId: bookId);
      } catch (e) {
        AnxLog.warning('Error preprocessing Chinese text chunk: $e');
      }
    }

    AnxLog.info(
        'Finished preprocessing ${chunks.length} chunks of Chinese text');
  }

  /// Store segmentation data for context menu use
  Future<void> _storeSegmentationDataForContextMenu({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
  }) async {
    try {
      final segmentationService = ChineseSegmentationService();
      await segmentationService.initialize();

      // Store comprehensive segmentation data for the selected text and surrounding context
      await segmentationService.storeSegmentationDataForSelection(
        selectedText: selectedText,
        fullNodeText: fullNodeText,
        startOffset: startOffset,
        endOffset: endOffset,
        selectionRange: selectionRange,
        bookId: widget.book.id,
      );

      AnxLog.info('Successfully stored segmentation data for context menu');
    } catch (e) {
      AnxLog.severe('Error storing segmentation data for context menu: $e');
    }
  }

  /// Split text into chunks of specified size
  List<String> _splitIntoChunks(String text, int chunkSize) {
    final List<String> chunks = [];
    for (int i = 0; i < text.length; i += chunkSize) {
      final end = (i + chunkSize < text.length) ? i + chunkSize : text.length;
      chunks.add(text.substring(i, end));
    }
    return chunks;
  }

  void onClick(Map<String, dynamic> location) {
    readingPageKey.currentState?.resetAwakeTimer();
    if (contextMenuEntry != null) {
      removeOverlay();
      return;
    }
    final x = location['x'];
    final y = location['y'];
    final part = coordinatesToPart(x, y);
    final currentPageTurningType = Prefs().pageTurningType;
    final pageTurningType = pageTurningTypes[currentPageTurningType];
    switch (pageTurningType[part]) {
      case PageTurningType.prev:
        // Pause auto-scroll on user interaction
        _pauseAutoScrollOnUserInteraction();
        prevPage();
        break;
      case PageTurningType.next:
        // Pause auto-scroll on user interaction
        _pauseAutoScrollOnUserInteraction();
        nextPage();
        break;
      case PageTurningType.menu:
        widget.showOrHideAppBarAndBottomBar(true);
        break;
    }
  }

  void _pauseAutoScrollOnUserInteraction() {
    // Stop auto-scroll and update preferences to disabled
    stopAutoScroll();
    Prefs().autoScrollEnabled = false;
  }

  Future<void> renderAnnotations(InAppWebViewController controller) async {
    try {
      List<BookNote> annotationList =
          await selectBookNotesByBookId(widget.book.id);

      AnxLog.info(
          'Rendering ${annotationList.length} annotations for book ${widget.book.title}');

      // Filter out invalid annotations and create safe JSON
      List<Map<String, dynamic>> safeAnnotations = [];

      for (BookNote note in annotationList) {
        try {
          // Validate required fields
          if (note.cfi.isNotEmpty &&
              note.type.isNotEmpty &&
              note.color.isNotEmpty) {
            safeAnnotations.add({
              'id': note.id,
              'note': note.content,
              'value': note.cfi,
              'type': note.type,
              'color': '#${note.color}',
            });
          } else {
            AnxLog.warning(
                'Skipping invalid annotation: id=${note.id}, cfi="${note.cfi}", type="${note.type}", color="${note.color}"');
          }
        } catch (e) {
          AnxLog.warning('Error processing annotation ${note.id}: $e');
        }
      }

      AnxLog.info('Filtered to ${safeAnnotations.length} valid annotations');

      String allAnnotations =
          jsonEncode(safeAnnotations).replaceAll('\'', '\\\'');

      // Use the enhanced method that passes data directly to avoid timing issues
      await controller.evaluateJavascript(source: '''
        try {
          const annotationsData = $allAnnotations;
          console.log('Flutter: Rendering', annotationsData.length, 'annotations');

          // Set global variable for compatibility
          window.allAnnotations = annotationsData;

          // Use the enhanced method that accepts data directly
          if (window.reader && window.reader.renderAnnotationWithData) {
            window.reader.renderAnnotationWithData(annotationsData);
          } else {
            // Fallback to original method
            window.renderAnnotations();
          }
        } catch (error) {
          console.error('Error rendering annotations:', error);
        }
      ''');

      AnxLog.info('Annotations rendering completed successfully');
    } catch (e) {
      AnxLog.severe('Error in renderAnnotations: $e');
      // Log more details about the error
      try {
        List<BookNote> annotationList =
            await selectBookNotesByBookId(widget.book.id);
        for (int i = 0; i < annotationList.length; i++) {
          BookNote note = annotationList[i];
          AnxLog.info(
              'Annotation $i: id=${note.id}, cfi=${note.cfi}, type=${note.type}, color=${note.color}, content=${note.content}');
        }
      } catch (debugError) {
        AnxLog.severe('Error during debug logging: $debugError');
      }
    }
  }

  Future<void> getThemeColor() async {
    if (Prefs().autoAdjustReadingTheme) {
      List<ReadTheme> themes = await selectThemes();
      final isDayMode =
          Theme.of(navigatorKey.currentContext!).brightness == Brightness.light;
      backgroundColor =
          isDayMode ? themes[0].backgroundColor : themes[1].backgroundColor;
      textColor = isDayMode ? themes[0].textColor : themes[1].textColor;
    } else {
      backgroundColor = Prefs().readTheme.backgroundColor;
      textColor = Prefs().readTheme.textColor;
    }
    setState(() {});
  }

  Future<void> setHandler(InAppWebViewController controller) async {
    String uri = Uri.encodeComponent(widget.book.fileFullPath);
    String url = 'http://localhost:${Server().port}/book/$uri';
    String initialCfi = widget.cfi ?? widget.book.lastReadPosition;

    await getThemeColor();

    webviewInitialVariable(
      controller,
      url,
      initialCfi,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );

    // Add handler to get word boundary for a specific position
    controller.addJavaScriptHandler(
      handlerName: 'getWordBoundaryForPosition',
      callback: (args) async {
        try {
          final String text = args[0];
          final int position = args[1];

          // Use dictionary service to get boundary
          final segmentationService = ChineseSegmentationService();
          await segmentationService.initialize(); // Ensure it's initialized

          final boundary = await segmentationService.getWordBoundaryForPosition(
              text, position);
          AnxLog.info(
              'Found boundary $boundary for position $position in "${text.substring(0, min(20, text.length))}..."');

          return boundary;
        } catch (e) {
          AnxLog.severe('Error getting word boundary: $e');
          if (args.length >= 2) {
            final position = args[1];
            return [position, position + 1]; // Default to single character
          }
          return [0, 1]; // Fallback
        }
      },
    );

    // Add handler to store segmentation data for context menu
    controller.addJavaScriptHandler(
      handlerName: 'storeSegmentationData',
      callback: (args) async {
        try {
          final Map<String, dynamic> data = Map<String, dynamic>.from(args[0]);
          final String selectedText = data['selectedText'] ?? '';
          final String fullNodeText = data['fullNodeText'] ?? '';
          final int startOffset = data['startOffset'] ?? 0;
          final int endOffset = data['endOffset'] ?? 0;
          final List<dynamic> selectionRange = data['selectionRange'] ?? [0, 0];

          AnxLog.info(
              'Storing segmentation data for selected text: "$selectedText"');

          // Store the segmentation data for later use in context menu
          await _storeSegmentationDataForContextMenu(
            selectedText: selectedText,
            fullNodeText: fullNodeText,
            startOffset: startOffset,
            endOffset: endOffset,
            selectionRange: [selectionRange[0], selectionRange[1]],
          );

          return true;
        } catch (e) {
          AnxLog.severe('Error storing segmentation data: $e');
          return false;
        }
      },
    );

    // Handler for getting word boundaries for segmentation mode
    controller.addJavaScriptHandler(
      handlerName: 'getWordBoundariesForSelection',
      callback: (args) async {
        try {
          final Map<String, dynamic> data = args[0];
          final String selectedText = data['selectedText'] ?? '';
          final String fullNodeText = data['fullNodeText'] ?? '';
          final int startOffset = data['startOffset'] ?? 0;
          final int endOffset = data['endOffset'] ?? 0;

          AnxLog.info('Getting word boundaries for selection: "$selectedText"');

          // Use the segmentation service to get word boundaries
          final segmentationService = ChineseSegmentationService();
          await segmentationService.initialize();

          // Get word boundaries for the full node text
          final boundaries =
              await segmentationService.getWordBoundaries(fullNodeText);

          // Find the best word boundary that encompasses the selection
          int adjustedStart = startOffset;
          int adjustedEnd = endOffset;

          for (final boundary in boundaries) {
            final wordStart = boundary[0];
            final wordEnd = boundary[1];

            // Check if this word boundary overlaps with the selection
            if (wordStart <= startOffset && wordEnd >= endOffset) {
              // Selection is entirely within this word
              adjustedStart = wordStart;
              adjustedEnd = wordEnd;
              break;
            } else if (wordStart <= startOffset && wordEnd > startOffset) {
              // Selection starts within this word
              adjustedStart = wordStart;
            } else if (wordStart < endOffset && wordEnd >= endOffset) {
              // Selection ends within this word
              adjustedEnd = wordEnd;
            }
          }

          AnxLog.info('Adjusted boundaries: [$adjustedStart, $adjustedEnd]');

          return {
            'adjustedStart': adjustedStart,
            'adjustedEnd': adjustedEnd,
          };
        } catch (e) {
          AnxLog.severe('Error getting word boundaries: $e');
          return null;
        }
      },
    );

    controller.addJavaScriptHandler(
        handlerName: 'onLoadEnd',
        callback: (args) {
          widget.onLoadEnd();

          // Preemptively process visible Chinese text
          preprocessVisibleContent();

          // Add a delayed annotation rendering to ensure highlights persist after book reopening
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              AnxLog.info(
                  'Delayed annotation rendering for highlight persistence');
              renderAnnotations(controller);
            }
          });
        });

    controller.addJavaScriptHandler(
        handlerName: 'onRelocated',
        callback: (args) {
          Map<String, dynamic> location = args[0];
          if (cfi == location['cfi']) return;
          setState(() {
            cfi = location['cfi'];
            percentage = location['percentage'] ?? 0.0;
            chapterTitle = location['chapterTitle'] ?? '';
            chapterHref = location['chapterHref'] ?? '';
            chapterCurrentPage = location['chapterCurrentPage'];
            chapterTotalPages = location['chapterTotalPages'];
          });
          saveReadingProgress();
          readingPageKey.currentState?.resetAwakeTimer();

          // Check bookmark status for the new page
          checkCurrentPageBookmark();

          // Preemptively process visible Chinese text when page changes
          preprocessVisibleContent();

          // Ensure annotations remain visible after page navigation
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              renderAnnotations(controller);
            }
          });
        });
    controller.addJavaScriptHandler(
        handlerName: 'onClick',
        callback: (args) {
          Map<String, dynamic> location = args[0];
          onClick(location);
        });
    controller.addJavaScriptHandler(
        handlerName: 'onSetToc',
        callback: (args) {
          List<dynamic> t = args[0];
          toc = t.map((i) => TocItem.fromJson(i)).toList();
        });
    controller.addJavaScriptHandler(
        handlerName: 'onSelectionEnd',
        callback: (args) {
          removeOverlay();
          Map<String, dynamic> location = args[0];
          String cfi = location['cfi'];
          String text = location['text'];
          bool footnote = location['footnote'];
          double x = location['pos']['point']['x'];
          double y = location['pos']['point']['y'];
          String dir = location['pos']['dir'];
          showUnifiedContextMenu(context, x, y, dir, text, cfi, null, footnote);
        });
    controller.addJavaScriptHandler(
        handlerName: 'onAnnotationClick',
        callback: (args) {
          Map<String, dynamic> annotation = args[0];
          int id = annotation['annotation']['id'];
          String cfi = annotation['annotation']['value'];
          String note = annotation['annotation']['note'];
          double x = annotation['pos']['point']['x'];
          double y = annotation['pos']['point']['y'];
          String dir = annotation['pos']['dir'];
          showUnifiedContextMenu(context, x, y, dir, note, cfi, id, false);
        });
    controller.addJavaScriptHandler(
      handlerName: 'onSearch',
      callback: (args) {
        Map<String, dynamic> search = args[0];
        setState(() {
          if (search['process'] != null) {
            searchProcess = search['process'].toDouble();
            _searchProgressController.add(searchProcess);
          } else {
            searchResult.add(SearchResultModel.fromJson(search));
            _searchResultController.add(searchResult);
          }
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'renderAnnotations',
      callback: (args) {
        renderAnnotations(controller);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onPushState',
      callback: (args) {
        Map<String, dynamic> state = args[0];
        canGoBack = state['canGoBack'];
        canGoForward = state['canGoForward'];
        if (!mounted) return;
        setState(() {
          showHistory = true;
        });
        Future.delayed(const Duration(seconds: 20), () {
          if (!mounted) return;
          setState(() {
            showHistory = false;
          });
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onImageClick',
      callback: (args) {
        String image = args[0];
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ImageViewer(
                      image: image,
                      bookName: widget.book.title,
                    )));
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onFootnoteClose',
      callback: (args) {
        removeOverlay();
      },
    );

    // Auto-scroll event handlers
    controller.addJavaScriptHandler(
      handlerName: 'onAutoScrollStart',
      callback: (args) {
        // Auto-scroll started successfully
        if (mounted) {
          // Optional: Show a subtle indicator that auto-scroll is active
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'onAutoScrollStop',
      callback: (args) {
        // Auto-scroll stopped
        if (mounted) {
          // Optional: Hide auto-scroll indicator
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'onAutoScrollEnd',
      callback: (args) {
        // Auto-scroll reached end of book
        if (mounted) {
          // Disable auto-scroll in preferences
          Prefs().autoScrollEnabled = false;
          // Show panels to let user know auto-scroll ended
          widget.showOrHideAppBarAndBottomBar(true);
        }
      },
    );

    // Bookmark-related handlers
    controller.addJavaScriptHandler(
      handlerName: 'handleBookmark',
      callback: (args) async {
        try {
          Map<String, dynamic> bookmarkData = args[0];
          String content = bookmarkData['content'] ?? '';
          String cfi = bookmarkData['cfi'] ?? '';
          String chapter = bookmarkData['chapter'] ?? '';
          double percentage = (bookmarkData['percentage'] ?? 0.0).toDouble();

          final bookmark = BookmarkModel(
            bookId: book.id,
            content: content,
            cfi: cfi,
            chapter: chapter,
            percentage: percentage,
            updateTime: DateTime.now(),
          );

          await ref
              .read(BookmarkProvider(book.id).notifier)
              .addBookmark(bookmark);

          // Update local state immediately
          setState(() {
            bookmarkExists = true;
            bookmarkCfi = cfi;
          });

          // Update reading page state
          widget.updateParent();

          AnxLog.info('Bookmark added: $content');
        } catch (e) {
          AnxLog.severe('Error handling bookmark: $e');
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'checkBookmark',
      callback: (args) async {
        try {
          String cfi = args[0];
          final bookmarks =
              ref.read(BookmarkProvider(book.id)).valueOrNull ?? [];
          final exists = bookmarks.any((bookmark) => bookmark.cfi == cfi);

          setState(() {
            bookmarkExists = exists;
            bookmarkCfi = cfi;
          });

          // Show or hide visual indicator based on bookmark existence
          if (exists) {
            showBookmarkIndicator();
          } else {
            hideBookmarkIndicator();
          }

          // Update reading page state
          widget.updateParent();

          return exists;
        } catch (e) {
          AnxLog.severe('Error checking bookmark: $e');
          return false;
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'removeBookmark',
      callback: (args) async {
        try {
          Map<String, dynamic> bookmarkData = args[0];
          String cfi = bookmarkData['cfi'] ?? '';

          // Remove bookmark from database
          ref.read(BookmarkProvider(book.id).notifier).removeBookmark(cfi: cfi);

          // Update local state immediately
          setState(() {
            bookmarkExists = false;
            bookmarkCfi = '';
          });

          // Update reading page state
          widget.updateParent();

          AnxLog.info('Bookmark removed: $cfi');
        } catch (e) {
          AnxLog.severe('Error removing bookmark: $e');
        }
      },
    );
  }

  Future<void> onWebViewCreated(InAppWebViewController controller) async {
    // Android-specific debug setting
    if (defaultTargetPlatform == TargetPlatform.android) {
      await InAppWebViewController.setWebContentsDebuggingEnabled(true);
    }
    webViewController = controller;
    setHandler(controller);
  }

  void removeOverlay() {
    if (contextMenuEntry == null || contextMenuEntry?.mounted == false) return;
    contextMenuEntry?.remove();
    contextMenuEntry = null;
  }

  void _handleKeyAndMouseEvents(KeyEvent event) {
    final nextPageEvent = [
      LogicalKeyboardKey.arrowRight,
      LogicalKeyboardKey.arrowDown,
      LogicalKeyboardKey.pageDown,
      LogicalKeyboardKey.space,
    ];

    final prevPageEvent = [
      LogicalKeyboardKey.arrowLeft,
      LogicalKeyboardKey.arrowUp,
      LogicalKeyboardKey.pageUp,
    ];

    final appBarEvent = [
      LogicalKeyboardKey.enter,
    ];

    if (event is KeyDownEvent) {
      if (nextPageEvent.contains(event.logicalKey)) {
        nextPage();
      } else if (prevPageEvent.contains(event.logicalKey)) {
        prevPage();
      } else if (appBarEvent.contains(event.logicalKey)) {
        widget.showOrHideAppBarAndBottomBar(true);
      }
    }
  }

  Future<void> _handlePointerEvents(PointerEvent event) async {
    if (await isFootNoteOpen() || Prefs().pageTurnStyle == PageTurn.scroll) {
      return;
    }
    if (event is PointerScrollEvent) {
      // Pause auto-scroll on manual scroll
      _pauseAutoScrollOnUserInteraction();

      if (event.scrollDelta.dy > 0) {
        nextPage();
      } else {
        prevPage();
      }
    }
  }

  @override
  void initState() {
    book = widget.book;
    focusNode.requestFocus();

    contextMenu = ContextMenu(
      settings: ContextMenuSettings(hideDefaultSystemContextMenuItems: true),
      onCreateContextMenu: (hitTestResult) async {
        // webViewController.evaluateJavascript(source: "showContextMenu()");
      },
      onHideContextMenu: () {
        // removeOverlay();
      },
    );
    if (Prefs().openBookAnimation) {
      _animationController = AnimationController(
        duration: DesignSystem.durationSlow + const Duration(milliseconds: 100),
        vsync: this,
      );
      _animation =
          Tween<double>(begin: 1.0, end: 0.0).animate(_animationController!);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animationController!.forward();
      });
    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future<void> saveReadingProgress() async {
    if (cfi == '') return;
    Book book = widget.book;
    book.lastReadPosition = cfi;
    book.readingPercentage = percentage;
    await updateBook(book);
    if (mounted) {
      ref.read(bookListProvider.notifier).refresh();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _animationController?.dispose();
    if (defaultTargetPlatform == TargetPlatform.android ||
        defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS) {
      InAppWebViewController.clearAllCache();
    }
    saveReadingProgress();
    removeOverlay();
  }

  String indexHtmlPath =
      "http://localhost:${Server().port}/foliate-js/index.html";

  InAppWebViewSettings initialSettings = InAppWebViewSettings(
    supportZoom: false,
    transparentBackground: true,
    isInspectable: kDebugMode,
  );

  void changeReadingInfo() {
    setState(() {});
  }

  Widget readingInfoWidget() {
    if (chapterCurrentPage == 0) {
      return const SizedBox();
    }

    TextStyle textStyle = TextStyle(
      color: Color(int.parse('0x$textColor')).withAlpha(150),
      fontSize: 10,
    );

    Widget chapterTitleWidget = Text(
      (chapterCurrentPage == 1 ? widget.book.title : chapterTitle),
      style: textStyle,
    );

    Widget chapterProgressWidget = Text(
      '$chapterCurrentPage/$chapterTotalPages',
      style: textStyle,
    );

    Widget bookProgressWidget =
        Text('${(percentage * 100).toStringAsFixed(2)}%', style: textStyle);

    Widget timeWidget() => StreamBuilder(
        stream: Stream.periodic(const Duration(seconds: 1)),
        builder: (context, snapshot) {
          String currentTime = DateFormat('HH:mm').format(DateTime.now());
          return Text(currentTime, style: textStyle);
        });

    Widget batteryWidget = FutureBuilder(
        future: Battery().batteryLevel,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Stack(
              alignment: Alignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(0, 0.8, 2, 0),
                  child: Text('${snapshot.data}',
                      style: TextStyle(
                        color: Color(int.parse('0x$textColor')),
                        fontSize: 9,
                      )),
                ),
                Icon(
                  HeroIcons.battery_0,
                  size: 27,
                  color: Color(int.parse('0x$textColor')),
                ),
              ],
            );
          } else {
            return const SizedBox();
          }
        });

    Widget batteryAndTimeWidget() => Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            batteryWidget,
            const SizedBox(width: 5),
            timeWidget(),
          ],
        );

    Widget getWidget(ReadingInfoEnum readingInfoEnum) {
      switch (readingInfoEnum) {
        case ReadingInfoEnum.chapterTitle:
          return chapterTitleWidget;
        case ReadingInfoEnum.chapterProgress:
          return chapterProgressWidget;
        case ReadingInfoEnum.bookProgress:
          return bookProgressWidget;
        case ReadingInfoEnum.battery:
          return batteryWidget;
        case ReadingInfoEnum.time:
          return timeWidget();
        case ReadingInfoEnum.batteryAndTime:
          return batteryAndTimeWidget();
        case ReadingInfoEnum.none:
          return const SizedBox();
      }
    }

    List<Widget> headerWidgets = [
      getWidget(Prefs().readingInfo.headerLeft),
      getWidget(Prefs().readingInfo.headerCenter),
      getWidget(Prefs().readingInfo.headerRight),
    ];

    List<Widget> footerWidgets = [
      getWidget(Prefs().readingInfo.footerLeft),
      getWidget(Prefs().readingInfo.footerCenter),
      getWidget(Prefs().readingInfo.footerRight),
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 10, 20, 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: headerWidgets,
            ),
          ),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: footerWidgets,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: focusNode,
      onKeyEvent: _handleKeyAndMouseEvents,
      child: Listener(
        onPointerSignal: (event) {
          _handlePointerEvents(event);
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              SizedBox.expand(
                child: InAppWebView(
                  webViewEnvironment: webViewEnvironment,
                  initialUrlRequest: URLRequest(url: WebUri(indexHtmlPath)),
                  initialSettings: initialSettings,
                  contextMenu: contextMenu,
                  // onLoadStop: (controller, url) => onWebViewCreated(controller),
                  onConsoleMessage: (controller, consoleMessage) {
                    if (consoleMessage.message.contains('loadBook')) {
                      onWebViewCreated(controller);
                    }
                    webviewConsoleMessage(controller, consoleMessage);
                  },
                ),
              ),
              readingInfoWidget(),
              if (showHistory)
                Positioned(
                  bottom: 30,
                  left: 0,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (canGoBack)
                          IconButton(
                            onPressed: () {
                              backHistory();
                            },
                            icon: const Icon(Icons.arrow_back_ios),
                          ),
                        if (canGoForward)
                          IconButton(
                            onPressed: () {
                              forwardHistory();
                            },
                            icon: const Icon(Icons.arrow_forward_ios),
                          ),
                      ],
                    ),
                  ),
                ),
              if (Prefs().openBookAnimation)
                SizedBox.expand(
                  child: Prefs().openBookAnimation
                      ? IgnorePointer(
                          ignoring: true,
                          child: FadeTransition(
                              opacity: _animation!,
                              child: bookCover(context, widget.book)),
                        )
                      : bookCover(context, widget.book),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
