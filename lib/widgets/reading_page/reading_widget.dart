import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/design_system_extensions.dart';
import 'package:dasso_reader/enums/text_selection_mode.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/performance/widget_optimizer.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:flutter/material.dart';

class ReadingWidget extends OptimizedStatefulWidget {
  const ReadingWidget({
    super.key,
    required this.epubPlayerKey,
    required this.onAutoScrollStateChanged,
    this.backgroundColor,
    this.textColor,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function(bool enabled, double speed) onAutoScrollStateChanged;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  OptimizedState<ReadingWidget> createState() => _ReadingWidgetState();
}

class _ReadingWidgetState extends OptimizedState<ReadingWidget> {
  BookStyle bookStyle = Prefs().bookStyle;
  bool autoScrollEnabled = false;
  double scrollSpeed = 5.0; // Default speed (1-10)

  @override
  void initState() {
    super.initState();
    // Load saved preferences
    autoScrollEnabled = Prefs().autoScrollEnabled;
    scrollSpeed = Prefs().autoScrollSpeed;
  }

  @override
  void dispose() {
    // Don't stop auto-scroll when widget is disposed - let ReadingPage manage it
    super.dispose();
  }

  @override
  Widget buildOptimized(BuildContext context) {
    return Padding(
      padding: ReadingDesign.controlsContainerPadding,
      child: _buildReadingOptions(),
    );
  }

  Widget _buildReadingOptions() {
    final txtColor = Theme.of(context).colorScheme.onSurface;

    return Column(
      children: [
        // Add consistent top padding for spacing
        DesignSystem.verticalSpaceL,
        // Page Turning Method Dropdown - Clean minimal style
        Padding(
          padding: EdgeInsets.symmetric(vertical: DesignSystem.spaceM),
          child: Row(
            children: [
              // Text label on the left
              Text(
                L10n.of(context).reading_page_page_turning_method,
                style: TextStyle(
                  color: txtColor,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              // Simple dropdown with no border
              DropdownButton<PageTurn>(
                value: Prefs().pageTurnStyle,
                icon: Icon(Icons.arrow_drop_down, color: txtColor),
                underline: Container(), // Remove the underline
                dropdownColor: widget.backgroundColor ??
                    Theme.of(context).colorScheme.surface,
                style: TextStyle(color: txtColor, fontSize: 16),
                onChanged: (PageTurn? value) {
                  if (value != null) {
                    Prefs().pageTurnStyle = value;
                    widget.epubPlayerKey.currentState!
                        .changePageTurnStyle(value);
                  }
                },
                items: PageTurn.values
                    .map<DropdownMenuItem<PageTurn>>((PageTurn value) {
                  return DropdownMenuItem<PageTurn>(
                    value: value,
                    child: Text(value.getLabel(context)),
                  );
                }).toList(),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // Auto-Scroll Toggle
        SwitchListTile(
          title: Text(
            L10n.of(context).reading_page_auto_scroll,
            style: TextStyle(
              color: Prefs().pageTurnStyle == PageTurn.scroll
                  ? txtColor
                  : txtColor.withValues(alpha: 0.5),
            ),
          ),
          subtitle: Prefs().pageTurnStyle != PageTurn.scroll
              ? Text(
                  L10n.of(context).reading_page_auto_scroll_scroll_mode_only,
                  style: TextStyle(
                    color: txtColor.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                )
              : null,
          value: autoScrollEnabled && Prefs().pageTurnStyle == PageTurn.scroll,
          activeColor: Theme.of(context).colorScheme.primary,
          onChanged: Prefs().pageTurnStyle == PageTurn.scroll
              ? (value) {
                  // Use debounced setState to prevent rapid rebuilds
                  setStateDebounced(() {
                    autoScrollEnabled = value;
                  });

                  // Notify ReadingPage about the state change
                  widget.onAutoScrollStateChanged(value, scrollSpeed);
                }
              : null, // Disable when not in scroll mode
        ),

        // Scroll Speed Slider
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              // Custom icon and text with conditional styling
              SizedBox(
                width: 48.0,
                height: 60.0,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.speed,
                      color: Prefs().pageTurnStyle == PageTurn.scroll
                          ? txtColor
                          : txtColor.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 4),
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        L10n.of(context).reading_page_scroll_speed,
                        style: TextStyle(
                          color: Prefs().pageTurnStyle == PageTurn.scroll
                              ? txtColor
                              : txtColor.withValues(alpha: 0.5),
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 36.0,
                    thumbShape: RoundSliderThumbShape(
                      enabledThumbRadius: 18.0,
                      elevation: 2.0,
                    ),
                    overlayShape:
                        const RoundSliderOverlayShape(overlayRadius: 26.0),
                    trackShape: const RoundedRectSliderTrackShape(),
                    activeTrackColor: txtColor.withValues(alpha: 0.25),
                    inactiveTrackColor: txtColor.withAlpha(25),
                    thumbColor: Colors.white,
                    overlayColor: txtColor.withAlpha(50),
                  ),
                  child: Slider(
                    value: scrollSpeed,
                    min: 1.0,
                    max: 10.0,
                    divisions: 9,
                    label: scrollSpeed.round().toString(),
                    onChanged: Prefs().pageTurnStyle == PageTurn.scroll
                        ? (value) {
                            // Use debounced setState for smooth slider interaction
                            setStateDebounced(() {
                              scrollSpeed = value;
                            },
                                delay:
                                    const Duration(milliseconds: 16)); // 60fps

                            // Notify ReadingPage about the speed change (immediate)
                            widget.onAutoScrollStateChanged(
                                autoScrollEnabled, value);
                          }
                        : null, // Disable when not in scroll mode
                  ),
                ),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // Text Selection Mode Toggle
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              // Icon and label
              SizedBox(
                width: 48.0,
                height: 60.0,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Prefs().textSelectionMode ==
                              TextSelectionMode.segmentation
                          ? Icons.auto_fix_high_outlined
                          : Icons.text_fields_outlined,
                      color: txtColor,
                    ),
                    const SizedBox(height: 4),
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        Prefs().textSelectionMode ==
                                TextSelectionMode.segmentation
                            ? L10n.of(context).reading_page_segmentation_mode
                            : L10n.of(context).reading_page_free_selection_mode,
                        style: TextStyle(
                          color: txtColor,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // Toggle switch
              Expanded(
                child: SwitchListTile(
                  title: Text(
                    Prefs().textSelectionMode == TextSelectionMode.segmentation
                        ? L10n.of(context).reading_page_switch_to_free_selection
                        : L10n.of(context).reading_page_switch_to_segmentation,
                    style: TextStyle(color: txtColor, fontSize: 14),
                  ),
                  value: Prefs().textSelectionMode ==
                      TextSelectionMode.segmentation,
                  activeColor: Theme.of(context).colorScheme.primary,
                  onChanged: (value) {
                    final newMode = value
                        ? TextSelectionMode.segmentation
                        : TextSelectionMode.free;

                    // Update preference
                    Prefs().textSelectionMode = newMode;

                    // Notify EpubPlayer about the mode change
                    widget.epubPlayerKey.currentState
                        ?.setTextSelectionMode(newMode);

                    // Trigger rebuild to update UI
                    setStateDebounced(() {});
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ),

        // Add bottom padding for consistent spacing
        SizedBox(height: DesignSystem.spaceL),
      ],
    );
  }
}
