import 'dart:io';

import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:flutter/material.dart';

Widget bookCover(
  BuildContext context,
  Book book, {
  double? height,
  double? width,
  double? radius,
}) {
  radius ??= 8;
  File file = File(book.coverFullPath);
  Widget child = file.existsSync()
      ? Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        )
      : _BookCoverPlaceholder(book: book);

  return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7),
          border: Border.all(
            width: 0.3,
            color: Colors.grey,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: child,
      ));
}

/// Optimized book cover placeholder that only rebuilds when book changes
///
/// This widget is extracted to minimize rebuilds and uses const constructor
/// for better performance.
class _BookCoverPlaceholder extends StatelessWidget {
  const _BookCoverPlaceholder({
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context) {
    final backgroundColor = Colors
        .primaries[book.title.hashCode % Colors.primaries.length].shade200;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Icon(
          Icons.book,
          size: 40,
          color: ColorSystem.getContrastingColor(backgroundColor),
        ),
      ),
    );
  }
}
